/**
 * Email templates for various application features
 */

export interface ContactFormData {
  fullName: string;
  email: string;
  phone: string;
  service: string;
  budget: string;
  projectDetails: string;
}

export interface CohortFormData {
  name: string;
  email: string;
  companyName: string;
  companyUrl?: string;
  aboutYourself: string;
  whatAreYouBuilding: string;
  fundingStage: string;
  cohortGoals: string;
  raiseTimeline: string;
  additionalInfo?: string;
}

// Helper functions to get readable labels
export const getServiceLabel = (serviceValue: string): string => {
  const services: Record<string, string> = {
    "web-design": "Web Design",
    "web-development": "Web Development",
    "branding": "Branding",
    "ui-ux": "UI/UX Design",
    "ecommerce": "E-commerce",
    "consultation": "Consultation"
  };
  return services[serviceValue] || serviceValue;
};

export const getBudgetLabel = (budgetValue: string): string => {
  const budgets: Record<string, string> = {
    "5k-10k": "$5,000 - $10,000",
    "10k-25k": "$10,000 - $25,000",
    "25k-50k": "$25,000 - $50,000",
    "50k-100k": "$50,000 - $100,000",
    "100k+": "$100,000+"
  };
  return budgets[budgetValue] || budgetValue;
};

// Helper functions for cohort form
export const getFundingStageLabel = (stageValue: string): string => {
  const stages: Record<string, string> = {
    "pre-seed": "Pre-seed",
    "seed": "Seed",
    "series-a": "Series A",
    "bootstrapped": "Bootstrapped",
    "other": "Other"
  };
  return stages[stageValue] || stageValue;
};

export const getRaiseTimelineLabel = (timelineValue: string): string => {
  const timelines: Record<string, string> = {
    "immediately": "Immediately (1-3 months)",
    "soon": "Soon (3-6 months)",
    "later": "Later (6+ months)",
    "not-sure": "Not sure yet"
  };
  return timelines[timelineValue] || timelineValue;
};

/**
 * Creates HTML email template for contact form submissions
 */
export const createContactEmailHtml = (data: ContactFormData): string => {
  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>New Project Inquiry</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
    }
    .container {
      padding: 20px;
      border: 1px solid #eaeaea;
      border-radius: 5px;
    }
    .header {
      background-color: #ff8a4c;
      color: white;
      padding: 15px;
      border-radius: 5px 5px 0 0;
      margin: -20px -20px 20px;
    }
    .section {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eaeaea;
    }
    .section:last-child {
      border-bottom: none;
    }
    .label {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .value {
      margin-bottom: 15px;
    }
    .project-details {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2 style="margin: 0;">New Project Inquiry</h2>
    </div>
    
    <div class="section">
      <div class="label">Name:</div>
      <div class="value">${data.fullName}</div>
      
      <div class="label">Email:</div>
      <div class="value">${data.email}</div>
      
      <div class="label">Phone:</div>
      <div class="value">${data.phone || "Not provided"}</div>
    </div>
    
    <div class="section">
      <div class="label">Service Required:</div>
      <div class="value">${getServiceLabel(data.service)}</div>
      
      <div class="label">Project Budget:</div>
      <div class="value">${getBudgetLabel(data.budget)}</div>
    </div>
    
    <div class="section">
      <div class="label">Project Details:</div>
      <div class="project-details">${data.projectDetails}</div>
    </div>
  </div>
</body>
</html>
  `;
};

/**
 * Creates plain text email for contact form submissions
 */
export const createContactEmailText = (data: ContactFormData): string => {
  return `
New Contact Form Submission

Name: ${data.fullName}
Email: ${data.email}
Phone: ${data.phone || "Not provided"}
Service: ${getServiceLabel(data.service)}
Budget: ${getBudgetLabel(data.budget)}

Project Details:
${data.projectDetails}
  `;
};

/**
 * Creates HTML email template for cohort application submissions
 */
export const createCohortEmailHtml = (data: CohortFormData): string => {
  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>New Cohort Application</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
    }
    .container {
      padding: 20px;
      border: 1px solid #eaeaea;
      border-radius: 5px;
    }
    .header {
      background-color: #3b82f6;
      color: white;
      padding: 15px;
      border-radius: 5px 5px 0 0;
      margin: -20px -20px 20px;
    }
    .section {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eaeaea;
    }
    .section:last-child {
      border-bottom: none;
    }
    .label {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .value {
      margin-bottom: 15px;
    }
    .text-area {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2 style="margin: 0;">New Founder Cohort Application</h2>
    </div>

    <div class="section">
      <h3>Applicant Details</h3>
      <div class="label">Name:</div>
      <div class="value">${data.name}</div>

      <div class="label">Email:</div>
      <div class="value">${data.email}</div>

      <div class="label">Company Name:</div>
      <div class="value">${data.companyName}</div>

      <div class="label">Company URL:</div>
      <div class="value">${data.companyUrl || "Not provided"}</div>
    </div>

    <div class="section">
      <h3>About the Applicant</h3>
      <div class="label">About Yourself:</div>
      <div class="text-area">${data.aboutYourself}</div>
    </div>

    <div class="section">
      <h3>Startup Information</h3>
      <div class="label">What are you building:</div>
      <div class="text-area">${data.whatAreYouBuilding}</div>

      <div class="label">Funding Stage:</div>
      <div class="value">${getFundingStageLabel(data.fundingStage)}</div>
    </div>

    <div class="section">
      <h3>Cohort Goals</h3>
      <div class="label">Goals for the cohort:</div>
      <div class="text-area">${data.cohortGoals}</div>

      <div class="label">Raise Timeline:</div>
      <div class="value">${getRaiseTimelineLabel(data.raiseTimeline)}</div>
    </div>

    ${data.additionalInfo ? `
    <div class="section">
      <h3>Additional Information</h3>
      <div class="text-area">${data.additionalInfo}</div>
    </div>
    ` : ''}
  </div>
</body>
</html>
  `;
};

/**
 * Creates plain text email for cohort application submissions
 */
export const createCohortEmailText = (data: CohortFormData): string => {
  return `
New Founder Cohort Application

APPLICANT DETAILS
Name: ${data.name}
Email: ${data.email}
Company Name: ${data.companyName}
Company URL: ${data.companyUrl || "Not provided"}

ABOUT THE APPLICANT
${data.aboutYourself}

STARTUP INFORMATION
What are you building:
${data.whatAreYouBuilding}

Funding Stage: ${getFundingStageLabel(data.fundingStage)}

COHORT GOALS
Goals for the cohort:
${data.cohortGoals}

Raise Timeline: ${getRaiseTimelineLabel(data.raiseTimeline)}

${data.additionalInfo ? `ADDITIONAL INFORMATION
${data.additionalInfo}` : ''}
  `;
};