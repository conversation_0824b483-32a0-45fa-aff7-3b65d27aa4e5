
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Mail, ArrowRight } from "lucide-react";

const ReferralCTA = () => {
  return (
    <section className="py-32 px-6 bg-gray-50">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-5xl md:text-6xl font-black text-gray-900 mb-8 leading-tight">
          Let's work together
        </h2>
        
        <p className="text-xl text-gray-600 mb-12 leading-relaxed max-w-2xl mx-auto">
          I'm always interested in new opportunities and exciting projects. Whether you're a startup or an established company, let's discuss how we can bring your vision to life.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
          <Button 
            size="lg" 
            className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-6 text-lg rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2"
          >
            <Mail className="w-5 h-5" />
            Start a project
            <ArrowRight className="w-5 h-5" />
          </Button>
          <Button 
            variant="outline"
            size="lg" 
            className="border-gray-300 hover:border-gray-400 text-gray-700 px-8 py-6 text-lg rounded-full transition-all duration-300"
          >
            Schedule a call
          </Button>
        </div>
        
        <div className="p-8 bg-white rounded-2xl shadow-sm">
          <p className="text-gray-600 mb-4">
            Prefer email? Reach out directly
          </p>
          <a 
            href="mailto:<EMAIL>" 
            className="text-lg font-medium text-gray-900 hover:text-gray-600 transition-colors"
          >
            <EMAIL>
          </a>
        </div>
      </div>
    </section>
  );
};

export default ReferralCTA;
