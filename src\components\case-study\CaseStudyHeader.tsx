
import { ArrowLef<PERSON> } from "lucide-react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

interface CaseStudyHeaderProps {
  title: string;
  subtitle: string;
  tagline: string;
  heroImage: string;
}

const CaseStudyHeader = ({ title, subtitle, tagline, heroImage }: CaseStudyHeaderProps) => {
  return (
    <header className="bg-white border-b border-gray-100">
      {/* Navigation */}
      <nav className="max-w-6xl mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-medium text-gray-900">Alex Designer</h2>
          <Link to="/">
            <Button variant="ghost" className="flex items-center gap-2 text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-4 h-4" />
              Back to Portfolio
            </Button>
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="max-w-6xl mx-auto px-6 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-black text-gray-900 mb-4 leading-tight">
            {title}
          </h1>
          <p className="text-xl text-blue-600 font-medium mb-6 uppercase tracking-wide">
            {subtitle}
          </p>
          <p className="text-xl md:text-2xl text-gray-600 font-light max-w-3xl mx-auto">
            {tagline}
          </p>
        </div>

        <div className="relative rounded-2xl overflow-hidden shadow-2xl">
          <img 
            src={heroImage}
            alt={title}
            className="w-full h-96 md:h-[500px] object-cover"
          />
        </div>
      </div>
    </header>
  );
};

export default CaseStudyHeader;
