
import { Message<PERSON>ir<PERSON>, <PERSON><PERSON>, Rocket } from "lucide-react";

const steps = [
  {
    icon: MessageCircle,
    title: "Discovery",
    description: "We talk about your vision, goals, and what makes your business unique. I listen carefully to understand your audience and aspirations."
  },
  {
    icon: Palette,
    title: "Design",
    description: "I create concepts and iterate based on your feedback. This collaborative process ensures the final design truly represents your brand."
  },
  {
    icon: Rocket,
    title: "Launch",
    description: "You receive all files, brand guidelines, and a website you're proud to share. Plus ongoing support to ensure everything runs smoothly."
  }
];

const Process = () => {
  return (
    <section className="py-20 px-6 bg-white">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light text-gray-900 mb-6">
            How we'll work together
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            A simple, collaborative process designed to bring your vision to life with minimal stress and maximum impact.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-12">
          {steps.map((step, index) => (
            <div key={index} className="text-center group">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-600 transition-colors duration-300">
                <step.icon className="w-8 h-8 text-blue-600 group-hover:text-white transition-colors duration-300" />
              </div>
              <h3 className="text-2xl font-medium text-gray-900 mb-4">
                {step.title}
              </h3>
              <p className="text-gray-700 leading-relaxed">
                {step.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Process;
