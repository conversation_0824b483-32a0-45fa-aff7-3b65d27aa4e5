
import { Lightbulb } from "lucide-react";

const KeyInsights = () => {
  const insights = [
    "The importance of balancing professionalism with approachability in local business branding",
    "How strategic color choices can communicate quality and warmth simultaneously", 
    "Why mobile-first design was crucial for a location-based business"
  ];

  return (
    <section className="py-20 border-t border-gray-100">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-6">
          Key Insights
        </h2>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Lessons learned from this project
        </p>
      </div>

      <div className="max-w-4xl mx-auto space-y-8">
        {insights.map((insight, index) => (
          <div key={index} className="bg-blue-50 border border-blue-100 rounded-xl p-8">
            <div className="flex items-start gap-4">
              <Lightbulb className="w-6 h-6 text-blue-600 mt-1 flex-shrink-0" />
              <p className="text-lg text-gray-800 leading-relaxed font-medium">
                {insight}
              </p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default KeyInsights;
