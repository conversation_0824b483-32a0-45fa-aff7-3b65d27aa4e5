/**
 * Postmark email service
 * Uses the Postmark API directly without dependencies
 */

interface PostmarkEmailOptions {
  from: string;
  to: string;
  subject: string;
  htmlBody?: string;
  textBody?: string;
  replyTo?: string;
  cc?: string;
  bcc?: string;
  tag?: string;
  metadata?: Record<string, string>;
  attachments?: Array<{
    name: string;
    content: string; // Base64 encoded content
    contentType: string;
  }>;
}

const serverToken = "a467142f-eb0c-4d02-8a8b-a954d1ba1d75";

/**
 * Sends a single email via Postmark API
 * 
 * Note: This function should be used from a server environment to avoid CORS issues.
 * For client-side usage, you should create a server endpoint that uses this function.
 * 
 * @param options Email options
 * @returns Response from Postmark API
 */
export async function sendEmail(options: PostmarkEmailOptions) {
  if (!options.htmlBody && !options.textBody) {
    throw new Error('Either htmlBody or textBody must be provided');
  }
  
  try {
    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': serverToken
      },
      body: JSON.stringify({
        From: options.from,
        To: options.to,
        Subject: options.subject,
        HtmlBody: options.htmlBody,
        TextBody: options.textBody,
        ReplyTo: options.replyTo,
        Cc: options.cc,
        Bcc: options.bcc,
        Tag: options.tag,
        Metadata: options.metadata,
        Attachments: options.attachments
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Postmark API error: ${errorData.Message || response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending email via Postmark:', error);
    throw error;
  }
}
