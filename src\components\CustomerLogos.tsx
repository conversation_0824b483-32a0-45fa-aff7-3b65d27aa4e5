
import React from 'react';

const CustomerLogos = () => {
  const logos = [
    { name: "<PERSON><PERSON><PERSON>", src: "/lovable-uploads/60141d1c-9263-48e5-b4c9-2946f29cb83c.webp" },
    { name: "Care.ai", src: "/lovable-uploads/45f42908-157f-4882-a499-981295e7cda4.webp" },
    { name: "Apiture", src: "/lovable-uploads/566d22bf-0560-4e4b-a12a-9db076a77e6d.webp" },
    { name: "ZoomInfo", src: "/lovable-uploads/eecd810c-5061-48b2-ad10-bd70d1d7d614.webp" },
    { name: "MetL<PERSON>", src: "/lovable-uploads/362517b1-510d-4678-9233-49072f8149f1.webp" },
    { name: "State Farm", src: "/lovable-uploads/9ba1a514-fb77-4145-b4b9-98a4a62ba636.webp" },
    { name: "<PERSON><PERSON>", src: "/lovable-uploads/752e889b-a3ae-4d08-9099-bcb535e6fcb3.webp" },
    { name: "ExxonMobil", src: "/lovable-uploads/4d5e4931-c355-44e4-9630-54762c7f0e8e.webp" },
    { name: "Caterpillar", src: "/lovable-uploads/bbf3ebac-a0b4-445f-8e22-6d3d7e3defd9.webp" },
    { name: "Epson", src: "/lovable-uploads/7eee012f-00fb-4f9a-b12c-48a603563302.webp" },
    { name: "Volvo", src: "/lovable-uploads/eb18be5a-e6dd-4885-a620-b196d1288a46.webp" },
  ];

  return (
    <section className="pb-16 overflow-hidden">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-12">
          <p className="text-sm text-gray-500 uppercase tracking-wider font-medium">
            TRUSTED BY ENTERPRISES, SCALE-UPS, AND STARTUPS WORLDWIDE
          </p>
        </div>
        
        <div className="relative">
          {/* Scrolling container */}
          <div className="flex animate-scroll" style={{ animationDuration: '60s' }}>
            <div className="flex space-x-16 items-center min-w-max">
              {logos.map((logo, index) => (
                <div
                  key={index}
                  className="flex items-center justify-center h-16"
                >
                  <img 
                    src={logo.src} 
                    alt={logo.name}
                    className="h-12 object-contain"
                    style={{ width: '175px' }}
                  />
                </div>
              ))}
            </div>
            {/* Duplicate for seamless loop */}
            <div className="flex space-x-16 items-center min-w-max ml-16">
              {logos.map((logo, index) => (
                <div
                  key={`duplicate-${index}`}
                  className="flex items-center justify-center h-16"
                >
                  <img 
                    src={logo.src} 
                    alt={logo.name}
                    className="h-12 object-contain"
                    style={{ width: '175px' }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CustomerLogos;
