
import { Quote } from "lucide-react";

const ClientTestimonial = () => {
  return (
    <section className="py-20 border-t border-gray-100">
      <div className="max-w-4xl mx-auto text-center">
        <Quote className="w-12 h-12 text-blue-600 mx-auto mb-8" />
        
        <blockquote className="text-2xl md:text-3xl font-light text-gray-900 mb-8 leading-relaxed">
          "Working with <PERSON> transformed not just our look, but how customers perceive our business. We finally have a brand that matches the quality of our coffee."
        </blockquote>
        
        <div className="flex items-center justify-center gap-4">
          <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
          <div className="text-left">
            <p className="font-medium text-gray-900"><PERSON></p>
            <p className="text-gray-600">Founder, Bloom Coffee Co.</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientTestimonial;
