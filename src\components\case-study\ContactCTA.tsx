
import { Mail } from "lucide-react";
import { Button } from "@/components/ui/button";

const ContactCTA = () => {
  return (
    <section className="py-20 bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <h2 className="text-3xl md:text-4xl font-light mb-6">
          Working on something similar?
        </h2>
        
        <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          I'd love to help you create a brand that connects with your community and stands out in your market.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button 
            size="lg" 
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-6 text-lg rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2"
          >
            <Mail className="w-5 h-5" />
            Start a Conversation
          </Button>
        </div>
        
        <div className="mt-8 p-6 bg-gray-800 rounded-2xl backdrop-blur-sm">
          <p className="text-gray-300 font-medium">
            <EMAIL>
          </p>
        </div>
      </div>
    </section>
  );
};

export default ContactCTA;
