# Portfolio Website with Email API

This project includes a React-based portfolio website with a Cloudflare Worker API for handling email submissions.

## Setup and Deployment

### Frontend (React)

1. Install dependencies:
   ```
   npm install
   ```

2. Run development server:
   ```
   npm run dev
   ```

3. Build for production:
   ```
   npm run build
   ```

### Email API (Cloudflare Worker)

The project includes a Cloudflare Worker that handles email sending via Postmark API.

#### Prerequisites

- Cloudflare account
- Postmark account and server token

#### Deploy the Worker

1. Build the worker:
   ```
   npm run build:worker
   ```

2. Deploy to Cloudflare:
   ```
   npm run deploy:worker
   ```

3. After deployment, update the `.env` file with your worker URL:
   ```
   VITE_EMAIL_API_URL=https://mario-portfolio-api.your-subdomain.workers.dev
   ```

#### Local Development

To test the worker locally:

```
npm run dev:worker
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Email API URL
VITE_EMAIL_API_URL=https://mario-portfolio-api.your-subdomain.workers.dev

# Postmark server token (for local worker development)
POSTMARK_SERVER_TOKEN=your-postmark-token
```

## Project Structure

- `/src` - Frontend React application
  - `/components` - Reusable UI components
  - `/pages` - Page components
  - `/lib` - Utility functions and services
  - `/worker` - Cloudflare Worker code for the email API

## Contact Form

The contact form in `src/pages/Contact.tsx` sends form data to the Cloudflare Worker API, which then forwards it to Postmark for email delivery.