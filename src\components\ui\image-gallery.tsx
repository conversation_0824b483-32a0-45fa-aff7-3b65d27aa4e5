import React, { useState } from 'react';
import OptimizedImage from './optimized-image';
import { cn } from '@/lib/utils';

interface ImageGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  className?: string;
  layout?: 'grid' | 'masonry' | 'carousel';
  priority?: boolean;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  className,
  layout = 'grid',
  priority = false
}) => {
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set());

  const handleImageLoad = (index: number) => {
    setLoadedImages(prev => new Set([...prev, index]));
  };

  const getLayoutClasses = () => {
    switch (layout) {
      case 'masonry':
        return 'columns-1 md:columns-2 lg:columns-3 gap-8 space-y-8';
      case 'carousel':
        return 'flex gap-4 overflow-x-auto snap-x snap-mandatory scrollbar-hide';
      default:
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8';
    }
  };

  return (
    <div className={cn('w-full', className)}>
      <div className={getLayoutClasses()}>
        {images.map((image, index) => (
          <div 
            key={index} 
            className={cn(
              'relative group cursor-pointer',
              layout === 'carousel' && 'flex-shrink-0 w-80 snap-center',
              layout === 'masonry' && 'break-inside-avoid mb-8'
            )}
          >
            <div className="relative overflow-hidden rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
              <OptimizedImage
                src={image.src}
                alt={image.alt}
                className={cn(
                  'w-full transition-transform duration-300 group-hover:scale-105',
                  layout === 'masonry' ? 'h-auto' : 'h-64 md:h-80'
                )}
                priority={priority && index < 3} // Prioritize first 3 images
                sizes={
                  layout === 'carousel' 
                    ? '320px' 
                    : '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
                }
                onLoad={() => handleImageLoad(index)}
              />
              
              {/* Loading overlay */}
              {!loadedImages.has(index) && (
                <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-xl" />
              )}
            </div>
            
            {/* Caption */}
            {image.caption && (
              <p className="text-center text-gray-600 mt-4 font-medium">
                {image.caption}
              </p>
            )}
          </div>
        ))}
      </div>
      
      {/* Loading indicator */}
      {loadedImages.size < images.length && (
        <div className="mt-8 text-center">
          <div className="inline-flex items-center gap-2 text-gray-500 text-sm">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
            Loading images... ({loadedImages.size}/{images.length})
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGallery; 