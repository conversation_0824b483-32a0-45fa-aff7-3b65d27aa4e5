import { useEffect } from "react";
import { Shield, Eye, MessageCircle, ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Navigation from "@/components/Navigation";
import ContactFooter from "@/components/ContactFooter";
import ProjectOverview from "@/components/case-study/ProjectOverview";

const NymbusCaseStudy = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="bg-white">
      <Navigation />

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-blue-50/30 pt-24">
        <div className="max-w-7xl mx-auto px-6 py-20">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            {/* Left Column - Content */}
            <div className="relative z-10">
              {/* Logo */}
              <div className="mb-8 animate-fade-in">
                <img 
                  src="/lovable-uploads/7a2f6822-02ad-4b83-9931-276647779cd9.webp"
                  alt="NYMBUS Logo"
                  className="w-24 h-24 rounded-3xl shadow-xl hover-scale"
                />
              </div>

              {/* Title */}
              <h1 className="text-6xl md:text-7xl font-black text-gray-900 mb-6 leading-[0.9] tracking-tight">
                NYMBUS
              </h1>
              
              {/* Subtitle */}
              <p className="text-2xl md:text-3xl text-gray-700 font-light mb-8 leading-relaxed">
                Reinventing Core Banking with
                <span className="block bg-gradient-to-r from-blue-600 to-blue-500 bg-clip-text text-transparent font-medium">
                  Bold Design and a Clear Story
                </span>
              </p>

              {/* Tags */}
              <div className="flex flex-wrap gap-3 mb-12">
                <span className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full text-sm font-semibold shadow-lg hover-scale">
                  Fintech
                </span>
                <span className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full text-sm font-semibold shadow-lg hover-scale">
                  Banking
                </span>
                <span className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full text-sm font-semibold shadow-lg hover-scale">
                  Series C
                </span>
              </div>

              {/* Info Grid */}
              <div className="grid md:grid-cols-3 gap-8">
                <div className="group">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">What I Did</h3>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"></div>
                      Branding
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"></div>
                      Landing Page
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"></div>
                      Deck
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"></div>
                      Product Design
                    </li>
                  </ul>
                </div>

                <div className="group">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">Industry</h3>
                  <p className="text-blue-600 font-semibold mb-2 text-lg">Fintech</p>
                  <p className="text-gray-600">Core Banking Solutions</p>
                </div>

                <div className="group">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">Raised</h3>
                  <p className="text-3xl font-black text-gray-900 mb-2 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">$329M</p>
                  <p className="text-gray-600">Series A through C funding</p>
                </div>
              </div>
            </div>

            {/* Right Column - Interface Screenshot */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-3xl blur-3xl transform rotate-6"></div>
              <div className="relative bg-white rounded-3xl overflow-hidden shadow-2xl border border-gray-100 hover-scale">
                <img 
                  src="/lovable-uploads/a9b3b4d0-828a-43ea-bf50-731b0421e365.webp"
                  alt="NYMBUS Banking Interface"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <ProjectOverview 
        overview={{
          challenge: "When I first got introduced to the NYMBUS founding team, the company didn't even exist yet — not by that name, and not with the product it's known for today. They had an early-stage fintech concept around rewards programs, but quickly realized the real opportunity wasn't on the fringe — it was at the core. The banking core. But here's what every fintech founder learns the hard way: Banks don't just evaluate your technology — they evaluate whether you look like the kind of company that won't disappear in six months with their most sensitive data. The challenge wasn't just building great software. It was looking like the kind of partner that Fortune 500 banks would bet their infrastructure on. Every detail had to scream 'we've done this before' — from the first investor deck to the booth at industry conferences. One amateur-looking touchpoint could kill months of relationship building. In banking, trust isn't earned through scrappy startup charm. It's earned through looking unshakably competent from day one.",
          solution: "We positioned NYMBUS as a movement, not just a software company. The solution was crafting a narrative that gave banks permission to evolve.",
          timeline: "6 months from concept to market launch with full brand identity and platform.",
          results: "$329 Million raised across 3 funding rounds with 50+ banks adopting the platform."
        }}
        quote={{
          text: "How do we position ourselves as revolutionary in a space that fears revolution?",
          author: "NYMBUS Founder"
        }}
        challengeImage={{
          src: "/lovable-uploads/bc91f375-4911-4dba-bd97-00f884053d95.webp",
          alt: "Traditional bank building representing established financial institutions"
        }}
      />

      {/* The Solution Section */}
      <section className="py-24 bg-muted/30 relative overflow-hidden">
        <div className="absolute inset-0 bg-dot-pattern opacity-10"></div>
        <div className="max-w-6xl mx-auto px-6 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-3xl mb-8">
              <img 
                src="/lovable-uploads/43f8e84c-46bc-42d6-959f-40bd7c1fef55.webp" 
                alt="NYMBUS icon" 
                className="w-12 h-12"
              />
            </div>
            <h2 className="text-5xl md:text-6xl font-bold text-foreground mb-6 tracking-tight">
              The Solution: Crafting a Movement
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-primary via-primary to-primary/60 mx-auto rounded-full"></div>
          </div>

          {/* Opening narrative */}
          <div className="max-w-4xl mx-auto mb-20">
            <div className="text-center space-y-6 text-lg text-muted-foreground leading-relaxed">
              <p className="text-xl font-medium text-foreground">
                We weren't just designing a product. We were crafting a movement — a narrative that gave banks permission to evolve and investors a clear disruption story they could champion.
              </p>
              <p>
                To do that, we had to reposition NYMBUS as the story of what's next — not just another software company trying to compete on features.
              </p>
              <p>
                This wasn't about optimizing capabilities. It was about signaling to the market that the future had arrived, and NYMBUS was leading it.
              </p>
            </div>
          </div>

          {/* Quote Section */}
          <div className="mb-20">
            <div className="relative max-w-3xl mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-accent/10 to-secondary/20 rounded-3xl blur-2xl"></div>
              <div className="relative bg-card/90 backdrop-blur-sm border-l-4 border-primary rounded-2xl p-12 shadow-xl text-center">
                <blockquote className="text-3xl font-bold text-blue-600 mb-6 leading-relaxed">
                  "The old way is over. It's time to move forward."
                </blockquote>
                <p className="text-muted-foreground">
                  While legacy competitors were stuck defending why their decades-old approach was still relevant, NYMBUS was selling transformation itself. This positioning didn't just differentiate them — it made choosing anyone else feel like choosing the past.
                </p>
              </div>
            </div>
          </div>

          {/* Strategic Foundation */}
          <div className="mb-20">
            <h3 className="text-3xl font-bold text-foreground text-center mb-12">The Strategic Foundation</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-blue-50 to-purple-50 backdrop-blur-sm border border-blue-200/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-6">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Clear Identity</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Distinct from legacy solutions, but trustworthy enough for financial institutions.
                  </p>
                  <p className="text-sm text-gray-600">
                    Every visual element and brand touchpoint communicated enterprise-grade reliability while feeling refreshingly modern. Banks could confidently present NYMBUS to their boards as both the safe choice and the smart choice.
                  </p>
                </div>
              </div>
              
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-orange-50 to-red-50 backdrop-blur-sm border border-orange-200/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-6">
                    <Eye className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Bold Vision</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    A future-focused narrative that wasn't just about tech, but about transformation.
                  </p>
                  <p className="text-sm text-gray-600">
                    Instead of competing on technical specifications, NYMBUS owned the conversation about industry evolution. This gave investors a clear market disruption thesis and helped enterprise clients internally champion the decision to modernize.
                  </p>
                </div>
              </div>
              
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-green-50 to-emerald-50 backdrop-blur-sm border border-green-200/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Simple Message</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Making the complex transformation story simple enough for both CEOs and CTOs to champion internally.
                  </p>
                  <p className="text-sm text-gray-600">
                    Complex banking technology became "the platform that lets you focus on banking, not maintenance." Every stakeholder — from technical teams to executive leadership — could immediately grasp the value and advocate for the solution.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* The Impact */}
          <div className="mb-16">
            <h3 className="text-3xl font-bold text-foreground text-center mb-12">The Impact</h3>
            <div className="max-w-4xl mx-auto">
              <p className="text-lg text-muted-foreground mb-8 text-center">
                This strategic positioning created immediate competitive advantages:
              </p>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-card/60 backdrop-blur-sm border border-border/30 rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-3">For fundraising:</h4>
                  <p className="text-sm text-muted-foreground">
                    Investors saw a company positioned to own an entire market transformation, not just win customers one by one.
                  </p>
                </div>
                <div className="bg-card/60 backdrop-blur-sm border border-border/30 rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-3">For enterprise sales:</h4>
                  <p className="text-sm text-muted-foreground">
                    Banks could present NYMBUS internally as the obvious evolution, not a risky experiment with an unproven startup.
                  </p>
                </div>
                <div className="bg-card/60 backdrop-blur-sm border border-border/30 rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-3">For market perception:</h4>
                  <p className="text-sm text-muted-foreground">
                    While competitors scrambled to modernize their messaging, NYMBUS had already claimed the position of industry leader — before they even had the largest market share.
                  </p>
                </div>
              </div>
            </div>
          </div>

        </div>
      </section>

      {/* The Work Section */}
      <section className="py-24 bg-background relative overflow-hidden">
        <div className="max-w-6xl mx-auto px-6 relative">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-bold text-foreground mb-8 tracking-tight">
              The Work
            </h2>
            <div className="max-w-3xl mx-auto mb-12">
              <p className="text-xl text-muted-foreground leading-relaxed mb-8">
                We created a comprehensive identity system and product experience that positioned 
                NYMBUS as the future of banking — sophisticated enough for enterprise, yet human 
                enough to feel like a partner, not just a vendor.
              </p>
              
            </div>
          </div>

          {/* Work Grid */}
          <div className="grid md:grid-cols-2 gap-12 mb-16">
            {/* Name & Identity */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gray-900 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/5e8f2e2e-e158-492f-90c3-76576593c6bc.webp"
                    alt="NYMBUS Name & Identity"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Name & Identity</h3>
              <p className="text-muted-foreground leading-relaxed">
                I led the naming process that became NYMBUS — a name that felt fresh, modern, 
                and ownable in the banking space.
              </p>
            </div>

            {/* Logo & Brand System */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/33a455ba-9091-4150-b8b4-3681400bdf35.webp"
                    alt="NYMBUS Logo & Brand System"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Logo & Brand System</h3>
              <p className="text-muted-foreground leading-relaxed">
                A clean, modern logo and visual identity system that felt trustworthy, but not 
                traditional.
              </p>
            </div>

            {/* Website & Messaging */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/84e2126e-fda5-4a30-b1cb-ec71b722250f.webp"
                    alt="NYMBUS Website & Messaging"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Website & Messaging</h3>
              <p className="text-muted-foreground leading-relaxed">
                Designed the first landing page and brand story to position NYMBUS as the 
                evolution of core banking.
              </p>
            </div>

            {/* Investor Materials */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-white rounded-2xl overflow-hidden shadow-xl">
                  <img 
                    src="/lovable-uploads/e7faf706-bb37-4ed7-a76c-9a533fb28054.webp"
                    alt="NYMBUS investor materials presentation slide"
                    className="w-full h-auto"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Investor Materials</h3>
              <p className="text-muted-foreground leading-relaxed">
                Created pitch decks, investor kits, and collateral to support their early-stage 
                fundraising.
              </p>
            </div>

            {/* Sales & Marketing Collateral */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/3925448e-4623-4920-81d3-717b1810db16.webp"
                    alt="NYMBUS Sales & Marketing Collateral"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Sales & Marketing Collateral</h3>
              <p className="text-muted-foreground leading-relaxed">
                Brochures, one-pagers, and enterprise sales decks — all aligned to a consistent, 
                confident brand voice.
              </p>
            </div>

            {/* Product UI/UX */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-white rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/ead92520-709e-4bed-afd8-5c55a55c3c93.webp"
                    alt="NYMBUS Product UI/UX Interface"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Product UI/UX</h3>
              <p className="text-muted-foreground leading-relaxed">
                Designed early product interfaces for the core banking platform with usability and 
                vision in mind.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* The Result Section */}
      <section className="py-24 bg-gradient-to-br from-gray-900 via-gray-900 to-blue-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-dot-pattern opacity-10"></div>
        <div className="max-w-6xl mx-auto px-6 relative">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-full text-sm font-semibold mb-8">
              The Result
            </div>
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 tracking-tight">
              $329 Million Raised
            </h2>
            <p className="text-xl text-gray-300 leading-relaxed max-w-4xl mx-auto">
              NYMBUS evolved from a raw concept into one of the most disruptive fintech 
              platforms in modern banking — with story, clarity, and design at the heart of its rise.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* 3 Rounds */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-blue-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <h3 className="text-3xl font-bold text-white mb-4">3 Rounds</h3>
                <p className="text-gray-300 leading-relaxed">
                  From Seed to Series C over 4 years
                </p>
              </div>
            </div>

            {/* 50+ Banks */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-blue-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <h3 className="text-3xl font-bold text-white mb-4">50+ Banks</h3>
                <p className="text-gray-300 leading-relaxed">
                  Adopting the NYMBUS platform
                </p>
              </div>
            </div>

            {/* Category Leader */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-blue-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <h3 className="text-3xl font-bold text-white mb-4">Category Leader</h3>
                <p className="text-gray-300 leading-relaxed">
                  In modern core banking
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Footer for Case Study */}
      <footer className="py-32 px-6 bg-gray-50 text-gray-900">
        <div className="max-w-6xl mx-auto text-center">
          {/* Main CTA Section */}
          <div className="mb-20">
            <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-8 leading-tight">
              Ready to build a
              <br />
              fundable brand?
            </h2>
            
            <Button 
              className="bg-[#474787] hover:bg-[#3d3f73] text-white px-8 py-4 text-lg rounded-full font-semibold shadow-lg shadow-[#474787]/25 transition-all duration-200 hover:shadow-[#474787]/40 hover:scale-105 flex items-center gap-2 mx-auto mb-16 w-fit"
              asChild
            >
              <a href="/cohort-application">
                APPLY NOW
                <ArrowRight className="w-5 h-5" />
              </a>
            </Button>
          </div>

          {/* Navigation Links */}
          <div className="mb-16">
            <div className="flex flex-wrap justify-center gap-8 text-gray-600">
              <a href="#home" className="hover:text-gray-900 transition-colors">Home</a>
              <a href="#about" className="hover:text-gray-900 transition-colors">About</a>
              <a href="#works" className="hover:text-gray-900 transition-colors">Works</a>
              <a href="#contact" className="hover:text-gray-900 transition-colors">Contact</a>
            </div>
          </div>
          
          <div className="pt-8 border-t border-gray-200">
            <p className="text-gray-500 text-center">
              Copyright © 2025 Garcia Interactive. All Rights Reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default NymbusCaseStudy;