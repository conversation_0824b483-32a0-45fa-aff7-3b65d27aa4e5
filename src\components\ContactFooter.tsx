
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const ContactFooter = () => {
  return (
    <footer className="py-32 px-6 bg-gray-50 text-gray-900">
      <div className="max-w-6xl mx-auto text-center">
        {/* Main CTA Section */}
        <div className="mb-20">
          <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-8 leading-tight">
            Ready to transform
            <br />
            your business?
          </h2>
          
          <Button 
            className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 text-base rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2 mx-auto mb-16 w-fit"
            asChild
          >
            <Link to="/contact">
              Start Your Project Now
              <ArrowRight className="w-5 h-5" />
            </Link>
          </Button>
        </div>

        {/* Navigation Links */}
        <div className="mb-16">
          <div className="flex flex-wrap justify-center gap-8 text-gray-600">
            <a href="#home" className="hover:text-gray-900 transition-colors">Home</a>
            <a href="#about" className="hover:text-gray-900 transition-colors">About</a>
            <a href="#works" className="hover:text-gray-900 transition-colors">Works</a>
            <a href="#contact" className="hover:text-gray-900 transition-colors">Contact</a>
          </div>
        </div>
        
        <div className="pt-8 border-t border-gray-200">
          <p className="text-gray-500 text-center">
            Copyright © 2025 Garcia Interactive. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default ContactFooter;
