import { useEffect } from 'react';

interface UseImagePreloadOptions {
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

export const useImagePreload = (
  src: string | string[], 
  options: UseImagePreloadOptions = {}
) => {
  const { priority = false, onLoad, onError } = options;

  useEffect(() => {
    if (!priority) return;

    const sources = Array.isArray(src) ? src : [src];
    const images: HTMLImageElement[] = [];

    sources.forEach((imageSrc) => {
      const img = new Image();
      
      img.onload = () => {
        onLoad?.();
      };
      
      img.onerror = () => {
        onError?.();
      };
      
      // Start preloading
      img.src = imageSrc;
      images.push(img);
    });

    // Cleanup function
    return () => {
      images.forEach(img => {
        img.onload = null;
        img.onerror = null;
      });
    };
  }, [src, priority, onLoad, onError]);
};

// Hook for preloading multiple images with progress tracking
export const useImagePreloadWithProgress = (
  sources: string[],
  options: UseImagePreloadOptions = {}
) => {
  const { priority = false, onLoad, onError } = options;

  useEffect(() => {
    if (!priority || sources.length === 0) return;

    let loadedCount = 0;
    const totalCount = sources.length;
    const images: HTMLImageElement[] = [];

    sources.forEach((src) => {
      const img = new Image();
      
      img.onload = () => {
        loadedCount++;
        if (loadedCount === totalCount) {
          onLoad?.();
        }
      };
      
      img.onerror = () => {
        loadedCount++;
        onError?.();
        if (loadedCount === totalCount) {
          onLoad?.(); // Still call onLoad even if some images failed
        }
      };
      
      img.src = src;
      images.push(img);
    });

    return () => {
      images.forEach(img => {
        img.onload = null;
        img.onerror = null;
      });
    };
  }, [sources, priority, onLoad, onError]);
};

// Function to create preload link tags in document head
export const preloadImage = (src: string, priority: boolean = false) => {
  if (typeof document === 'undefined') return;

  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'image';
  link.href = src;
  
  if (priority) {
    link.setAttribute('fetchpriority', 'high');
  }

  document.head.appendChild(link);

  return () => {
    if (document.head.contains(link)) {
      document.head.removeChild(link);
    }
  };
}; 